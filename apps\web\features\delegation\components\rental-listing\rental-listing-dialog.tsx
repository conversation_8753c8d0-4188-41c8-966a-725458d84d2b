"use client";

import { useState, useEffect } from "react";
import { Button, Modal, Input, Select } from "ui";
import { Calculator, Clock, Coins, Shield } from "lucide-react";
import { useCreateRental } from "../../hooks/useCreateRental";
import {
  ICreateRentalFormData,
  ERewardDistributionType,
  EGameRewardDistributionType,
  EDelegatedTaskType,
  REWARD_DISTRIBUTION_LABELS,
  DELEGATED_TASK_LABELS,
} from "../../types/delegation.types";
import { IChickenMetadata } from "@/lib/types/chicken.types";

interface IRentalListingDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  chickenTokenId?: number;
  chickenMetadata?: IChickenMetadata;
  onSuccess?: () => void;
}

export function RentalListingDialog({
  isOpen,
  onOpenChange,
  chickenTokenId,
  chickenMetadata,
  onSuccess,
}: IRentalListingDialogProps) {
  const { executeCreateRental, validateFormData, isCreating } =
    useCreateRental();

  const [formData, setFormData] = useState<ICreateRentalFormData>({
    chickenTokenId: chickenTokenId || null,
    roninPrice: "",
    rentalPeriod: 86400, // 1 day default
    rewardDistribution: ERewardDistributionType.DELEGATOR_ONLY,
    gameRewardDistribution: EGameRewardDistributionType.DELEGATOR_ONLY,
    delegatedTask: EDelegatedTaskType.BOTH,
    isDirectDelegation: false,
    sharedRewardAmount: 5, // Default value
    renterAddress: "",
  });

  const [insurancePrice, setInsurancePrice] = useState("");
  const [errors, setErrors] = useState<string[]>([]);

  // Update chicken token ID when prop changes
  useEffect(() => {
    if (chickenTokenId) {
      setFormData((prev) => ({ ...prev, chickenTokenId }));
    }
  }, [chickenTokenId]);

  // Duration options
  const durationOptions = [
    { value: 3600, label: "1 Hour" },
    { value: 86400, label: "1 Day" },
    { value: 259200, label: "3 Days" },
    { value: 604800, label: "1 Week" },
    { value: 1209600, label: "2 Weeks" },
    { value: 2592000, label: "30 Days" },
    { value: 7776000, label: "90 Days" },
  ];

  // Reward distribution options
  const rewardDistributionOptions = Object.entries(
    REWARD_DISTRIBUTION_LABELS
  ).map(([value, label]) => ({
    value: parseInt(value),
    label,
  }));

  // Delegated task options
  const delegatedTaskOptions = Object.entries(DELEGATED_TASK_LABELS).map(
    ([value, label]) => ({
      value: parseInt(value),
      label,
    })
  );

  // Helper function to check if selected chicken can die (needs insurance)
  const chickenNeedsInsurance = (): boolean => {
    if (!chickenMetadata?.attributes) return false;
    const typeAttribute = chickenMetadata.attributes.find(
      (attr) => attr.trait_type === "Type"
    );
    // Genesis chickens cannot die, so they don't need insurance
    // Legacy and Ordinary chickens can die, so they need insurance
    return (
      typeAttribute?.value === "Legacy" || typeAttribute?.value === "Ordinary"
    );
  };

  // Calculate total rental price and insurance
  const dailyRate = parseFloat(formData.roninPrice) || 0;
  const durationInDays = formData.rentalPeriod / 86400;
  const totalRentalPrice = dailyRate * durationInDays;
  const insuranceAmount = chickenNeedsInsurance()
    ? parseFloat(insurancePrice) || totalRentalPrice * 0.5 // Default 50% for chickens that can die
    : 0; // No insurance for Genesis chickens

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    const validationErrors = validateFormData(formData);

    // Additional validation for insurance (only for chickens that can die)
    if (
      chickenNeedsInsurance() &&
      insurancePrice &&
      parseFloat(insurancePrice) < 0
    ) {
      validationErrors.push("Insurance price cannot be negative");
    }

    setErrors(validationErrors);

    if (validationErrors.length > 0) {
      return;
    }

    try {
      // Include insurance price in form data
      const formDataWithInsurance = {
        ...formData,
        insurancePrice: chickenNeedsInsurance() ? insurancePrice : undefined,
      };

      const result = await executeCreateRental(formDataWithInsurance);
      if (result?.success) {
        onOpenChange(false);
        onSuccess?.();
        // Reset form
        setFormData({
          chickenTokenId: null,
          roninPrice: "",
          rentalPeriod: 86400,
          rewardDistribution: ERewardDistributionType.DELEGATOR_ONLY,
          gameRewardDistribution: EGameRewardDistributionType.DELEGATOR_ONLY,
          delegatedTask: EDelegatedTaskType.BOTH,
          isDirectDelegation: false,
          sharedRewardAmount: 5,
          renterAddress: "",
        });
        setInsurancePrice("");
      }
    } catch (error) {
      console.error("Failed to create rental:", error);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
    setErrors([]);
  };

  return (
    <Modal isOpen={isOpen} onOpenChange={handleClose}>
      <Modal.Content size="lg">
        <Modal.Header>
          <Modal.Title>List Chicken for Rent</Modal.Title>
          <Modal.Description>
            Set rental terms and list your chicken in the marketplace
          </Modal.Description>
        </Modal.Header>

        <Modal.Body className="space-y-6 max-h-96 overflow-y-auto">
          {/* Chicken Information */}
          {chickenMetadata && (
            <div className="bg-stone-700/30 rounded-lg p-4">
              <h3 className="text-sm font-medium text-white mb-2">
                Chicken Details
              </h3>
              <div className="flex items-center gap-3">
                <img
                  src={chickenMetadata.image}
                  alt={chickenMetadata.name}
                  className="w-12 h-12 rounded-lg border border-stone-600"
                />
                <div>
                  <div className="text-white font-medium">
                    {chickenMetadata.name}
                  </div>
                  <div className="text-gray-400 text-sm">
                    Token ID: #{chickenTokenId}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Pricing */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-white flex items-center gap-2">
              <Coins className="w-4 h-4" />
              Pricing
            </h3>

            <div
              className={`grid gap-4 ${chickenNeedsInsurance() ? "grid-cols-2" : "grid-cols-1"}`}
            >
              <div>
                <label className="block text-sm text-gray-400 mb-2">
                  Daily Rate (RON)
                </label>
                <Input
                  type="number"
                  step="0.0001"
                  min="0"
                  placeholder="0.0000"
                  value={formData.roninPrice}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      roninPrice: e.target.value,
                    }))
                  }
                />
              </div>

              {/* Insurance field - Only for chickens that can die (Legacy and Ordinary) */}
              {chickenNeedsInsurance() && (
                <div>
                  <label className="block text-sm text-gray-400 mb-2">
                    Insurance (RON)
                  </label>
                  <Input
                    type="number"
                    step="0.0001"
                    min="0"
                    placeholder={`${(totalRentalPrice * 0.5).toFixed(4)}`}
                    value={insurancePrice}
                    onChange={(e) => setInsurancePrice(e.target.value)}
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    Suggested: {(totalRentalPrice * 0.5).toFixed(4)} RON (50% of
                    total)
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Duration */}
          <div>
            <label className="flex text-sm text-gray-400 mb-2 items-center gap-2">
              <Clock className="w-4 h-4" />
              Rental Duration
            </label>
            <Select
              selectedKey={formData.rentalPeriod.toString()}
              onSelectionChange={(value) =>
                setFormData((prev) => ({
                  ...prev,
                  rentalPeriod: parseInt(value as string),
                }))
              }
            >
              <Select.Trigger />
              <Select.List>
                {durationOptions.map((option) => (
                  <Select.Option
                    key={option.value}
                    id={option.value.toString()}
                  >
                    {option.label}
                  </Select.Option>
                ))}
              </Select.List>
            </Select>
          </div>

          {/* Delegation Terms */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-white flex items-center gap-2">
              <Shield className="w-4 h-4" />
              Delegation Terms
            </h3>

            <div>
              <label className="block text-sm text-gray-400 mb-2">
                Allowed Tasks
              </label>
              <Select
                selectedKey={formData.delegatedTask.toString()}
                onSelectionChange={(value) =>
                  setFormData((prev) => ({
                    ...prev,
                    delegatedTask: parseInt(
                      value as string
                    ) as EDelegatedTaskType,
                  }))
                }
              >
                <Select.Trigger />
                <Select.List>
                  {delegatedTaskOptions.map((option) => (
                    <Select.Option
                      key={option.value}
                      id={option.value.toString()}
                    >
                      {option.label}
                    </Select.Option>
                  ))}
                </Select.List>
              </Select>
            </div>

            <div>
              <label className="block text-sm text-gray-400 mb-2">
                Reward Distribution
              </label>
              <Select
                selectedKey={formData.rewardDistribution.toString()}
                onSelectionChange={(value) =>
                  setFormData((prev) => ({
                    ...prev,
                    rewardDistribution: parseInt(
                      value as string
                    ) as ERewardDistributionType,
                  }))
                }
              >
                <Select.Trigger />
                <Select.List>
                  {rewardDistributionOptions.map((option) => (
                    <Select.Option
                      key={option.value}
                      id={option.value.toString()}
                    >
                      {option.label}
                    </Select.Option>
                  ))}
                </Select.List>
              </Select>
            </div>

            {formData.rewardDistribution === ERewardDistributionType.SHARED && (
              <div>
                <label className="block text-sm text-gray-400 mb-2">
                  Delegatee Daily Rub Rewards
                </label>
                <Input
                  type="number"
                  min="1"
                  max="100"
                  placeholder="50"
                  value={formData.sharedRewardAmount?.toString() || ""}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      sharedRewardAmount: parseInt(e.target.value) || 5,
                    }))
                  }
                />
                <div className="text-xs text-gray-500 mt-1">
                  Daily Rub reward amount for the renter (1-100)
                </div>
              </div>
            )}

            {/* Game Reward Distribution - Only show when gameplay is enabled */}
            {(formData.delegatedTask === EDelegatedTaskType.GAMEPLAY ||
              formData.delegatedTask === EDelegatedTaskType.BOTH) && (
              <div>
                <label className="block text-sm text-gray-400 mb-2">
                  Game Reward Distribution
                </label>
                <Select
                  selectedKey={formData.gameRewardDistribution.toString()}
                  onSelectionChange={(value) =>
                    setFormData((prev) => ({
                      ...prev,
                      gameRewardDistribution: parseInt(
                        value as string
                      ) as EGameRewardDistributionType,
                    }))
                  }
                >
                  <Select.Trigger />
                  <Select.List>
                    <Select.Option
                      key={EGameRewardDistributionType.DELEGATOR_ONLY}
                      id={EGameRewardDistributionType.DELEGATOR_ONLY.toString()}
                    >
                      Owner (You keep game rewards)
                    </Select.Option>
                    <Select.Option
                      key={EGameRewardDistributionType.DELEGATEE_ONLY}
                      id={EGameRewardDistributionType.DELEGATEE_ONLY.toString()}
                    >
                      Renter (Renter keeps game rewards)
                    </Select.Option>
                  </Select.List>
                </Select>
                <div className="text-xs text-gray-500 mt-1">
                  Who receives game rewards (crystals, shards, corn) from
                  gameplay
                </div>
              </div>
            )}
          </div>

          {/* Price Summary */}
          <div className="bg-stone-700/30 rounded-lg p-4">
            <h3 className="text-sm font-medium text-white mb-3 flex items-center gap-2">
              <Calculator className="w-4 h-4" />
              Price Summary
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Daily Rate:</span>
                <span className="text-white">{dailyRate.toFixed(4)} RON</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Duration:</span>
                <span className="text-white">{durationInDays} day(s)</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Total Rental:</span>
                <span className="text-white">
                  {totalRentalPrice.toFixed(4)} RON
                </span>
              </div>
              {chickenNeedsInsurance() && (
                <div className="flex justify-between">
                  <span className="text-gray-400">Insurance Required:</span>
                  <span className="text-white">
                    {insuranceAmount.toFixed(4)} RON
                  </span>
                </div>
              )}
              <div className="border-t border-stone-600 pt-2 flex justify-between font-medium">
                <span className="text-gray-300">Renter Pays:</span>
                <span className="text-yellow-400">
                  {(totalRentalPrice + insuranceAmount).toFixed(4)} RON
                </span>
              </div>
            </div>
          </div>

          {/* Errors */}
          {errors.length > 0 && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
              <div className="text-red-400 text-sm font-medium mb-1">
                Please fix the following errors:
              </div>
              <ul className="text-red-300 text-sm space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}
        </Modal.Body>

        <Modal.Footer>
          <Button
            appearance="outline"
            onPress={handleClose}
            isDisabled={isCreating}
          >
            Cancel
          </Button>
          <Button
            onPress={handleSubmit}
            isDisabled={isCreating || !chickenTokenId}
            className="bg-yellow-500 hover:bg-yellow-600 text-black"
          >
            {isCreating ? "Listing..." : "List for Rent"}
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
}
