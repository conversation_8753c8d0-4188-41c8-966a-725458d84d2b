"use client";

import { Loading } from "@/components/shared/loading";
import NotReady from "@/components/shared/not-ready";
import { useChickendOwned } from "@/hooks/useChickendOwned";
import { useStateContext } from "@/providers/app/state";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import BattleModal from "./battle-modal";
import { FeedModal } from "./feed-modal";
import useFoodCraftingStore from "@/store/food-crafting";
import useDailyFeedStore from "@/store/daily-feeding";
import HealModal from "./heal-modal";
import { CreateRentalModal } from "@/features/delegation/components/create-rental/create-rental-modal";
import { ChickenStats } from "@/types/chicken.type";

// Import our new hooks and components
import { useFavoriteChickens } from "@/hooks/useFavoriteChickens";
import { useChickenTimers } from "@/hooks/useChickenTimers";
import { useChickenStats } from "@/hooks/useChickenStats";
import { useHealInfo } from "@/hooks/useHealInfo";
import { InventoryControls } from "./components/InventoryControls";
import { ChickenGrid } from "./components/ChickenGrid";
import { InventoryPagination } from "./components/InventoryPagination";
import { useCancelDelegation } from "@/features/delegation/hooks/useCancelDelegation";
import { useUnlistChickenForRent } from "@/features/delegation/hooks/useUnlistChickenForRent";
import { useMyRentals } from "@/features/delegation/hooks/useMyRentals";
import { toast } from "sonner";

interface Chicken {
  tokenId: string;
  image: string;
  attributes?: {
    Type?: string[];
  };
}

type SortableAttribute =
  | "id"
  | "hp"
  | "level"
  | "attack"
  | "defense"
  | "speed"
  | "ferocity"
  | "cockrage"
  | "evasion"
  | "mmr";

// Helper function to check if chicken is an egg
const isEgg = (chicken: Chicken) => {
  return chicken?.attributes?.Type?.[0] === "egg";
};

export default function ChickenInventory() {
  const router = useRouter();
  const { address } = useStateContext();
  const { fetchFoodBalance } = useFoodCraftingStore();
  const { checkApproval } = useDailyFeedStore();

  // Data fetching
  const {
    isLoading: loading,
    data,
    error,
  } = useChickendOwned(address as string);

  // Delegation hooks
  const { executeCancelDelegation } = useCancelDelegation();
  const { executeUnlistChickenForRent } = useUnlistChickenForRent();
  const { ownedRentals } = useMyRentals(address);

  // Local state
  const [showFeedModal, setShowFeedModal] = useState<boolean>(false);
  const [selectedChicken, setSelectedChicken] = useState<number>();
  const [searchQuery, setSearchQuery] = useState("");
  const [isDelegateModalOpen, setIsDelegateModalOpen] =
    useState<boolean>(false);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;
  const [battleModalChicken, setBattleModalChicken] = useState<string | null>(
    null
  );
  const [healModalChicken, setHealModalChicken] = useState<string | null>(null);
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const [isNotReadyOpen, setNotReadyOpen] = useState(false);

  // Sorting state
  const [sortAttribute, setSortAttribute] = useState<SortableAttribute>(() => {
    const savedSort = localStorage.getItem("chickenSortAttribute");
    return (savedSort as SortableAttribute) || "id";
  });

  const [sortOrder, setSortOrder] = useState<"asc" | "desc">(() => {
    const savedOrder = localStorage.getItem("chickenSortOrder");
    return (savedOrder as "asc" | "desc") || "asc";
  });

  // Custom hooks
  const { toggleFavorite, isFavorite } = useFavoriteChickens();
  const {
    chickenStats,
    statsLoading,
    setChickenStats,
    fetchChickenStatsBatch,
    refetchChickenStats,
    isDead,
    isFaint,
    isBreeding,
    isListed,
    wasTransferredToday,
    wasListedToday,
    isImmortal,
  } = useChickenStats();

  const {
    cooldownTimers,
    recoveryTimers,
    breedingTimers,
    listedTimers,
    transferTimers,
    immortalTimers,
    setInitialTimers,
    formatTime: formatTimeFromHook,
  } = useChickenTimers(chickenStats);

  // Wrapper to ensure formatTime always returns a string
  const formatTime = (
    seconds: number,
    type:
      | "cooldown"
      | "recovery"
      | "breeding"
      | "listed"
      | "transfer"
      | "immortal"
  ): string => {
    return formatTimeFromHook(seconds, type) || "";
  };

  const { healInfo, healInfoLoading, fetchHealInfo, handleHeal } = useHealInfo(
    address || null
  );

  const hasRun = useRef(false);

  useEffect(() => {
    if (hasRun.current) return;
    hasRun.current = true;

    checkApproval();
    fetchFoodBalance();
  }, []);

  useEffect(() => {
    localStorage.setItem("chickenSortAttribute", sortAttribute);
    localStorage.setItem("chickenSortOrder", sortOrder);
  }, [sortAttribute, sortOrder]);

  // Calculate paginated chickens
  const chickens = useMemo(() => {
    if (!data?.tokens) return [];

    const sortedChickens = [...data.tokens].sort((a, b) => {
      // First handle eggs - always put them at the end
      const aIsEgg = isEgg(a);
      const bIsEgg = isEgg(b);

      if (aIsEgg && !bIsEgg) return 1;
      if (!aIsEgg && bIsEgg) return -1;
      if (aIsEgg && bIsEgg) {
        // If both are eggs, sort by ID
        return sortOrder === "asc"
          ? parseInt(a.tokenId) - parseInt(b.tokenId)
          : parseInt(b.tokenId) - parseInt(a.tokenId);
      }

      // For non-eggs, sort by the selected attribute
      if (sortAttribute === "id") {
        return sortOrder === "asc"
          ? parseInt(a.tokenId) - parseInt(b.tokenId)
          : parseInt(b.tokenId) - parseInt(a.tokenId);
      }

      // For battle stats, we need to access the chickenStats object
      const statsA = chickenStats[a.tokenId];
      const statsB = chickenStats[b.tokenId];

      // If stats aren't loaded yet, sort by ID as fallback
      if (!statsA || !statsB) {
        return sortOrder === "asc"
          ? parseInt(a.tokenId) - parseInt(b.tokenId)
          : parseInt(b.tokenId) - parseInt(a.tokenId);
      }

      // Fix: For all stats, we need to reverse the logic to match the expected behavior
      // When sortOrder is "asc", we want higher stats to come first
      // When sortOrder is "desc", we want lower stats to come first
      if (sortAttribute === "hp") {
        return sortOrder === "asc"
          ? statsB.hp - statsA.hp // Higher HP first for ascending
          : statsA.hp - statsB.hp; // Lower HP first for descending
      } else {
        // For all other stats (attack, defense, speed, level, mmr)
        const valueA = statsA[sortAttribute] || 0;
        const valueB = statsB[sortAttribute] || 0;
        return sortOrder === "asc"
          ? valueB - valueA // Higher stats first for ascending
          : valueA - valueB; // Lower stats first for descending
      }
    });

    return sortedChickens;
  }, [data, sortOrder, sortAttribute, chickenStats]);

  const ftChickens = useMemo(() => {
    if (searchQuery.trim() === "") {
      return chickens;
    } else {
      const filtered = chickens.filter((chicken) =>
        chicken.tokenId.includes(searchQuery.trim())
      );
      return filtered;
    }
  }, [chickens, searchQuery]);

  // Organize chickens with favorites at the top
  const organizedChickens = useMemo(() => {
    if (ftChickens.length === 0) return [];

    // Split into favorites and non-favorites
    const favoriteChickens = ftChickens.filter((chicken) =>
      isFavorite(chicken.tokenId)
    );

    const nonFavoriteChickens = ftChickens.filter(
      (chicken) => !isFavorite(chicken.tokenId)
    );

    // Combine with favorites at the top
    return [...favoriteChickens, ...nonFavoriteChickens];
  }, [ftChickens, isFavorite]);

  const totalPages = Math.ceil(organizedChickens.length / itemsPerPage);
  const paginatedChickens = organizedChickens.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Improved useEffect with debouncing to prevent too many API calls
  useEffect(() => {
    if (loading || paginatedChickens.length === 0) return;

    // Debounce the API call to prevent rapid calls during pagination/filtering
    const debounceTimer = setTimeout(async () => {
      // Get all token IDs from visible chickens
      const tokenIds = paginatedChickens.map((chicken) => chicken.tokenId);

      // Fetch stats in batch and get timer data
      const timerData = await fetchChickenStatsBatch(tokenIds);

      // Initialize timers if we got timer data back
      if (timerData) {
        setInitialTimers(
          timerData.newCooldowns,
          timerData.newRecoveryTimers,
          timerData.newBreedingTimers,
          timerData.newTransferTimers,
          timerData.newListedTimers,
          timerData.newImmortalTimers
        );
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(debounceTimer);
  }, [paginatedChickens, loading, fetchChickenStatsBatch, setInitialTimers]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      // Only close if clicking outside sort button
      const target = e.target as HTMLElement;
      if (!target.closest(".sort-button")) {
        setShowSortDropdown(false);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  // Handle chicken click
  const handleChickenClick = (tokenId: string) => {
    router.push(`/inventory/chickens/${tokenId}`);
  };

  // Handle battle action
  const handleBattle = (e: React.MouseEvent, tokenId: string) => {
    e.stopPropagation();
    e.preventDefault();

    // Check if chicken is disabled (priority order: dead → faint → transferred → listed → breeding)
    if (
      isDead(tokenId) ||
      isFaint(tokenId) ||
      wasTransferredToday(tokenId) ||
      isListed(tokenId) ||
      isBreeding(tokenId)
    ) {
      return; // Do nothing for disabled chickens
    }

    // Get chicken stats
    const stats = chickenStats[tokenId];

    // Check if HP is full
    if (stats && stats.hp >= stats.maxHp) {
      setBattleModalChicken(tokenId); // Show battle confirmation modal
    } else {
      // Show a notification that HP is not full
      alert(
        "Your chicken's HP must be at full health to battle. Please heal your chicken to max HP first."
      );
      // Alternatively, you could use a toast notification or a custom modal here
    }
  };

  const closeBattleModal = () => {
    setBattleModalChicken(null);
  };

  // Handle breed action
  const handleBreed = (e: React.MouseEvent, tokenId: string) => {
    e.stopPropagation();
    e.preventDefault();

    // Check if chicken is disabled (priority order: dead → faint → transferred → listed → breeding)
    if (
      isDead(tokenId) ||
      isFaint(tokenId) ||
      wasTransferredToday(tokenId) ||
      isListed(tokenId) ||
      isBreeding(tokenId)
    ) {
      return; // Do nothing for disabled chickens
    }

    router.push(`/breeding?parent1=${tokenId}`);
  };

  // Handle favorite toggle with event stopping
  const handleToggleFavorite = (e: React.MouseEvent, tokenId: string) => {
    e.stopPropagation();
    e.preventDefault();
    toggleFavorite(tokenId);
  };

  // Handle menu actions
  const handleMenuAction = (
    e: React.MouseEvent,
    action: string,
    tokenId: string
  ) => {
    e.stopPropagation();
    e.preventDefault();

    // Handle different actions
    switch (action) {
      case "feed":
        // Check if chicken is disabled (priority order: dead → faint → transferred → listed → breeding)
        if (
          isDead(tokenId) ||
          isFaint(tokenId) ||
          wasTransferredToday(tokenId) ||
          isListed(tokenId) ||
          isBreeding(tokenId)
        ) {
          return; // Do nothing for disabled chickens
        }

        setSelectedChicken(Number(tokenId));
        setShowFeedModal(true);
        break;
      case "delegate":
        // Open delegation modal with pre-selected chicken
        setSelectedChicken(Number(tokenId));
        setIsDelegateModalOpen(true);
        break;
      case "cancel-delegation": {
        // Find the rental for this chicken
        const delegationToCancel = ownedRentals.find(
          (rental) => rental.chickenTokenId.toString() === tokenId
        );
        if (delegationToCancel) {
          executeCancelDelegation(delegationToCancel.id);
        } else {
          toast.error("Delegation not found for this chicken");
        }
        break;
      }
      case "unlist-from-market": {
        // Find the rental listing for this chicken
        const listingToUnlist = ownedRentals.find(
          (rental) => rental.chickenTokenId.toString() === tokenId
        );
        if (listingToUnlist) {
          executeUnlistChickenForRent(listingToUnlist.id);
        } else {
          toast.error("Marketplace listing not found for this chicken");
        }
        break;
      }
      case "release":
        // Show confirmation dialog for release
        // if (confirm(`Are you sure you want to release Chicken #${tokenId}?`)) {
        //   console.log(`Release chicken ${tokenId}`);
        //   // Implement release logic
        // }
        setNotReadyOpen(true);
        break;
      default:
        break;
    }
  };

  // Add this function to open the heal modal
  const openHealModal = useCallback(
    (e: React.MouseEvent, tokenId: string) => {
      e.stopPropagation();
      e.preventDefault();

      // Check if chicken is disabled (priority order: dead → faint → listed → transferred → breeding)
      if (
        isDead(tokenId) ||
        isFaint(tokenId) ||
        isListed(tokenId) ||
        wasTransferredToday(tokenId) ||
        isBreeding(tokenId)
      ) {
        return; // Do nothing for disabled chickens
      }

      setHealModalChicken(tokenId);
      fetchHealInfo();
    },
    [fetchHealInfo, isDead, isFaint, wasTransferredToday, isListed, isBreeding]
  );

  const openFeedModal = useCallback(
    (e: React.MouseEvent, tokenId: string) => {
      e.stopPropagation();
      e.preventDefault();

      // Check if chicken is disabled (priority order: dead → faint → listed → transferred → breeding)
      if (
        isDead(tokenId) ||
        isFaint(tokenId) ||
        isListed(tokenId) ||
        wasTransferredToday(tokenId) ||
        isBreeding(tokenId)
      ) {
        return; // Do nothing for disabled chickens
      }

      setSelectedChicken(Number(tokenId));
      setShowFeedModal(true);
    },
    [isDead, isFaint, wasTransferredToday, isListed, isBreeding]
  );

  // Add this function to close the heal modal
  const closeHealModal = useCallback(() => {
    setHealModalChicken(null);
  }, []);

  // Add this function to perform the heal
  const performHeal = useCallback(async () => {
    if (!healModalChicken) return false;
    return await handleHeal(healModalChicken, setChickenStats);
  }, [healModalChicken, handleHeal, setChickenStats]);

  // Pagination navigation
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  if (loading) return <Loading />;

  if (error)
    return (
      <div className="text-center p-8">
        <p className="text-red-500 mb-2">Error loading chickens</p>
        <p className="text-white text-sm">{error.message}</p>
        <button
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          onClick={() => window.location.reload()}
        >
          Try Again
        </button>
      </div>
    );

  return (
    <div>
      {/* Controls */}
      <InventoryControls
        sortAttribute={sortAttribute}
        sortOrder={sortOrder}
        showSortDropdown={showSortDropdown}
        searchQuery={searchQuery}
        healInfo={healInfo}
        healInfoLoading={healInfoLoading}
        onSortAttributeChange={setSortAttribute}
        onSortOrderChange={() =>
          setSortOrder((prev) => (prev === "asc" ? "desc" : "asc"))
        }
        onShowSortDropdownChange={setShowSortDropdown}
        onSearchQueryChange={setSearchQuery}
        onRefreshHealInfo={fetchHealInfo}
      />

      <ChickenGrid
        chickens={paginatedChickens}
        chickenStats={chickenStats}
        statsLoading={statsLoading}
        cooldownTimers={cooldownTimers}
        recoveryTimers={recoveryTimers}
        breedingTimers={breedingTimers}
        listedTimers={listedTimers}
        transferTimers={transferTimers}
        immortalTimers={immortalTimers}
        formatTime={formatTime}
        isFavorite={isFavorite}
        isEgg={isEgg}
        isFaint={isFaint}
        isDead={isDead}
        isBreeding={isBreeding}
        isListed={isListed}
        wasTransferredToday={wasTransferredToday}
        wasListedToday={wasListedToday}
        isImmortal={isImmortal}
        onChickenClick={handleChickenClick}
        onToggleFavorite={handleToggleFavorite}
        onBattle={handleBattle}
        onHeal={openHealModal}
        onFeed={openFeedModal}
        onBreed={handleBreed}
        onMenuAction={handleMenuAction}
      />

      <InventoryPagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={goToPage}
      />
      {/* Battle Confirmation Modal */}
      <BattleModal
        isOpen={!!battleModalChicken}
        onClose={closeBattleModal}
        chickenId={battleModalChicken || ""}
        chickenImage={
          ftChickens.find((c) => c.tokenId === battleModalChicken)?.image
        }
        chickenStats={{
          level: chickenStats[battleModalChicken || ""]?.level,
          attack: chickenStats[battleModalChicken || ""]?.attack,
          defense: chickenStats[battleModalChicken || ""]?.defense,
          speed: chickenStats[battleModalChicken || ""]?.speed,
          ferocity: chickenStats[battleModalChicken || ""]?.ferocity,
          cockrage: chickenStats[battleModalChicken || ""]?.cockrage,
          evasion: chickenStats[battleModalChicken || ""]?.evasion,
          hp: chickenStats[battleModalChicken || ""]?.hp,
          maxHp: chickenStats[battleModalChicken || ""]?.maxHp,
          boosters: chickenStats[battleModalChicken || ""]?.boosters,
        }}
      />

      <NotReady
        isOpen={isNotReadyOpen}
        onClose={() => setNotReadyOpen(false)}
      />
      <FeedModal
        show={showFeedModal}
        setShow={setShowFeedModal}
        tokenId={selectedChicken!}
      />
      <HealModal
        isOpen={!!healModalChicken}
        onClose={closeHealModal}
        chickenId={healModalChicken || ""}
        chickenImage={
          ftChickens.find((c) => c.tokenId === healModalChicken)?.image
        }
        currentHp={chickenStats[healModalChicken || ""]?.hp}
        maxHp={chickenStats[healModalChicken || ""]?.maxHp}
        onHeal={performHeal}
        healInfo={healInfo}
        isLoading={healInfoLoading}
        refetch={() => refetchChickenStats(healModalChicken || "")}
        onBattle={() => {
          // Close heal modal and open battle modal using existing function
          if (healModalChicken) {
            setBattleModalChicken(healModalChicken);
          }
        }}
      />
      <CreateRentalModal
        isOpen={isDelegateModalOpen}
        onOpenChange={setIsDelegateModalOpen}
        preSelectedChickenId={selectedChicken}
      />
    </div>
  );
}
