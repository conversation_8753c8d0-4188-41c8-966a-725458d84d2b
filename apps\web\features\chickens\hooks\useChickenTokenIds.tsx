"use client";

import { useQuery, keepPreviousData } from "@tanstack/react-query";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import {
  chickenBulkFetcherAddress,
  chickenBulkFetchAbi,
} from "@/providers/web3/abi/chicken-bulk-fetcher";
import { IChickenTokenIdsByType } from "../types/chicken-info.types";
import { useMemo } from "react";

/**
 * Hook to fetch all chicken token IDs owned by an address using the bulk fetcher contract
 */
export const useChickenTokenIds = (address?: Address) => {
  const { publicClient } = useStateContext();

  // Fetch Legacy chicken token IDs
  const legacyTokenIdsQuery = useQuery({
    queryKey: ["chickenTokenIds", "legacy", address],
    queryFn: async () => {
      if (!address || !publicClient || !chickenBulkFetcherAddress) {
        return [];
      }

      try {
        const result = await publicClient.readContract({
          address: chickenBulkFetcherAddress as Address,
          abi: chickenBulkFetchAbi,
          functionName: "getLegacyChickenTokenIdsOfAddress",
          args: [address],
        });

        return (result as bigint[]).map((tokenId) => Number(tokenId));
      } catch (error) {
        console.error("Failed to fetch legacy chicken token IDs:", error);
        return [];
      }
    },
    enabled: !!address && !!publicClient && !!chickenBulkFetcherAddress,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchInterval: false,
    placeholderData: keepPreviousData,
  });

  // Fetch Genesis chicken token IDs
  const genesisTokenIdsQuery = useQuery({
    queryKey: ["chickenTokenIds", "genesis", address],
    queryFn: async () => {
      if (!address || !publicClient || !chickenBulkFetcherAddress) {
        return [];
      }

      try {
        const result = await publicClient.readContract({
          address: chickenBulkFetcherAddress as Address,
          abi: chickenBulkFetchAbi,
          functionName: "getGenesisChickenTokenIdsOfAddress",
          args: [address],
        });

        return (result as bigint[]).map((tokenId) => Number(tokenId));
      } catch (error) {
        console.error("Failed to fetch genesis chicken token IDs:", error);
        return [];
      }
    },
    enabled:
      !!address &&
      legacyTokenIdsQuery.isSuccess &&
      !!publicClient &&
      !!chickenBulkFetcherAddress,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchInterval: false,
    placeholderData: keepPreviousData,
  });

  // Combine and organize token IDs by type
  const tokenIdsByType: IChickenTokenIdsByType = useMemo(() => {
    const legacy = legacyTokenIdsQuery.data || [];
    const genesis = genesisTokenIdsQuery.data || [];

    // Note: Ordinary chickens are not fetched from contracts as they don't exist as separate NFTs
    // They are represented in the metadata of Legacy/Genesis chickens
    const ordinary: number[] = [];

    const all = [...legacy, ...genesis].sort((a, b) => a - b);

    return {
      ordinary,
      legacy,
      genesis,
      all,
    };
  }, [legacyTokenIdsQuery.data, genesisTokenIdsQuery.data]);

  // Determine loading state
  const isLoading =
    legacyTokenIdsQuery.isLoading || genesisTokenIdsQuery.isLoading;

  // Determine error state
  const error = legacyTokenIdsQuery.error || genesisTokenIdsQuery.error;

  // Determine success state
  const isSuccess =
    legacyTokenIdsQuery.isSuccess && genesisTokenIdsQuery.isSuccess;

  return {
    tokenIdsByType,
    isLoading,
    error,
    isSuccess,
    refetch: () => {
      legacyTokenIdsQuery.refetch();
      genesisTokenIdsQuery.refetch();
    },
    // Individual query results for debugging
    legacyQuery: legacyTokenIdsQuery,
    genesisQuery: genesisTokenIdsQuery,
  };
};

/**
 * Hook to fetch chicken token IDs for the connected wallet
 */
export const useConnectedWalletChickenTokenIds = () => {
  const { address, isConnected } = useStateContext();

  return useChickenTokenIds(isConnected ? address : undefined);
};
