"use client";

import { hookstate, useHookstate } from "@hookstate/core";
import { devtools } from "@hookstate/devtools";
import { useBreeding } from "../../breeding/hooks/useBreeding";
import useEggInfo from "./useEggInfo";
import useChickenMetadataBatch from "../../breeding/hooks/useChickenMetadataBatch";
import { useEggPhases } from "./useEggPhases";
import { useEggHatching } from "./useEggHatching";

const initialState = {};
const hatchingState = hookstate(
  initialState,
  devtools({ key: "hatchingState" })
);

export const useHatching = () => {
  const state = useHookstate(hatchingState);
  const { eggs, chickenQuery, allEggTokenIds } = useBreeding();
  const { eggInfoQuery, eggInfoMap } = useEggInfo(allEggTokenIds);

  const { hatchEgg, isHatching } = useEggHatching(eggInfoMap);

  // Fetch metadata for chickens
  const metadataQuery = useChickenMetadataBatch(allEggTokenIds);

  const { breedingPhaseEggs, hatchingPhaseEggs } = useEggPhases(
    allEggTokenIds,
    eggInfoMap
  );

  return {
    state,
    eggs,
    hatchEgg,
    isHatching,

    // Data
    eggInfoMap,
    metadataMap: metadataQuery.metadataMap,
    breedingPhaseEggs,
    hatchingPhaseEggs,

    // Loading and error states
    isLoading:
      chickenQuery.isFetching ||
      eggInfoQuery.isFetching ||
      metadataQuery.isLoading,
    error:
      chickenQuery.error ||
      eggInfoQuery.error ||
      metadataQuery.metadataQuery.error,
    isError:
      chickenQuery.isError ||
      eggInfoQuery.isError ||
      metadataQuery.metadataQuery.isError,
  };
};

export default hatchingState;
