"use client";

import { useMemo, useState, useCallback } from "react";
import { useStateContext } from "@/providers/app/state";
import { IChickenMetadata, IAttribute } from "@/lib/types/chicken.types";
import { useChickenTokenIds } from "@/features/chickens/hooks/useChickenTokenIds";
import { useQuery } from "@tanstack/react-query";
import { DelegationAPI } from "@/features/delegation/api/delegation.api";
import {
  ChickenCache,
  ICacheStats,
} from "../../../delegation/utils/chicken-cache";
import {
  IProgressiveChickenOptions,
  useConnectedWalletProgressiveChickenInfo,
} from "@/features/chickens/hooks/useProgressiveChickenInfo";
import { useBulkBattleStats } from "@/features/battle/hooks/useBattleStats";
import { getDailyFeathers } from "@/lib/utils/chicken-attributes";

// Interface for chicken rental status
export interface IChickenRentalStatus {
  isAvailable: boolean;
  rentalStatus?:
    | "available"
    | "listed"
    | "rented"
    | "delegated"
    | "expired"
    | "cancelled";
  statusLabel?: string;
}

// Interface for chicken data suitable for delegation (progressive version)
export interface IChickenForDelegationProgressive {
  tokenId: number;
  image: string;
  type?: string;
  level?: number;
  isAvailable?: boolean;
  rentalStatus?: IChickenRentalStatus;

  // These will be loaded progressively
  metadata?: IChickenMetadata;
  dailyFeathers?: number;
  breedCount?: number;
  cooldownEndsAt?: Date;
  winRate?: number;
  battleStats?: IBattleStats;
}

// Interface for battle stats
interface IBattleStats {
  wins: number;
  losses: number;
  draws?: number;
  level?: number;
  state?: string; // "normal", "faint", "dead", "breeding"
  recoverDate?: string;
  stats?: {
    attack?: number;
    defense?: number;
    speed?: number;
    currentHp?: number;
    hp?: number;
    cockrage?: number;
    ferocity?: number;
    evasion?: number;
    instinct?: string;
  };
}

// Progressive loading options for delegation
export interface IProgressiveDelegationOptions
  extends IProgressiveChickenOptions {
  filterType?: "all" | "ordinary" | "legacy" | "genesis";
  sortBy?: "tokenId" | "level" | "type";
  sortOrder?: "asc" | "desc";
}

const DEFAULT_DELEGATION_OPTIONS: IProgressiveDelegationOptions = {
  pageSize: 50,
  enableAutoLoad: true,
  preloadNextPage: true,
  filterType: "all",
  sortBy: "tokenId",
  sortOrder: "asc",
};

// Helper function to get chicken type from metadata
const getChickenTypeFromMetadata = (metadata: any): string => {
  const typeAttribute = metadata.attributes?.find(
    (attr: IAttribute) => attr.trait_type === "Type"
  );
  return typeAttribute?.value?.toLowerCase() || "ordinary";
};

// Helper function to get rental status from rental data
const getChickenRentalStatusFromData = (
  rentalData: any
): IChickenRentalStatus => {
  if (!rentalData) {
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  }

  // Check the rental status
  if (rentalData.status === 0) {
    // Status 0 = AVAILABLE (listed in rental market)
    return {
      isAvailable: false,
      rentalStatus: "listed",
      statusLabel: "Listed in Rental Market",
    };
  } else if (rentalData.status === 1) {
    // Status 1 = RENTED (actually rented by someone)
    // Check if it's a paid rental or free delegation
    const roninPrice = rentalData.roninPrice || rentalData.ronin_price;
    const isDelegation = roninPrice === "0" || roninPrice === 0;

    if (isDelegation) {
      return {
        isAvailable: false,
        rentalStatus: "delegated",
        statusLabel: "Already Delegated",
      };
    } else {
      return {
        isAvailable: false,
        rentalStatus: "rented",
        statusLabel: "Already Rented",
      };
    }
  } else if (rentalData.status === 2) {
    // Status 2 = EXPIRED
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  } else if (rentalData.status === 3) {
    // Status 3 = CANCELLED
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  }

  // Default to available for unknown statuses
  return {
    isAvailable: true,
    rentalStatus: "available",
    statusLabel: "Available",
  };
};

/**
 * Optimized hook that implements efficient chicken loading strategy:
 * 1. Load token IDs from contracts (Genesis/Legacy only) - lightweight
 * 2. Apply type filters and search on token IDs - instant filtering
 * 3. Paginate filtered token IDs - only process what's needed
 * 4. Load metadata only for current page token IDs - minimal API calls
 * 5. Load rental data only for current page token IDs - minimal API calls
 *
 * This approach is much more efficient than loading all metadata upfront
 * and then filtering, especially for users with many chickens.
 */
export function useOptimizedChickensForDelegation(
  searchQuery: string = "",
  options: Partial<IProgressiveDelegationOptions> = {}
) {
  const { address, isConnected } = useStateContext();
  const mergedOptions = { ...DEFAULT_DELEGATION_OPTIONS, ...options };
  const [currentPage, setCurrentPage] = useState(1);

  // Step 1: Load token IDs from contracts (Genesis/Legacy only)
  const {
    tokenIdsByType,
    isLoading: tokenIdsLoading,
    error: tokenIdsError,
  } = useChickenTokenIds(isConnected ? address : undefined);

  // Step 2: Apply type filter on token IDs
  const filteredTokenIds = useMemo(() => {
    const { genesis, legacy } = tokenIdsByType;

    switch (mergedOptions.filterType) {
      case "genesis":
        return genesis;
      case "legacy":
        return legacy;
      case "all":
      default:
        return [...genesis, ...legacy].sort((a, b) => a - b);
    }
  }, [tokenIdsByType, mergedOptions.filterType]);

  // Step 3: Apply search filter on token IDs (token ID search only at this stage)
  const searchFilteredTokenIds = useMemo(() => {
    if (!searchQuery.trim()) {
      return filteredTokenIds;
    }

    const query = searchQuery.toLowerCase();
    // For now, only search by token ID since we don't have metadata yet
    return filteredTokenIds.filter((tokenId) =>
      tokenId.toString().includes(query)
    );
  }, [filteredTokenIds, searchQuery]);

  // Step 4: Apply sorting
  const sortedTokenIds = useMemo(() => {
    const sorted = [...searchFilteredTokenIds];

    // For now, only sort by tokenId since we don't have metadata
    sorted.sort((a, b) => {
      const comparison = a - b;
      return mergedOptions.sortOrder === "desc" ? -comparison : comparison;
    });

    return sorted;
  }, [searchFilteredTokenIds, mergedOptions.sortOrder]);

  // Step 5: Calculate pagination
  const totalCount = sortedTokenIds.length;
  const totalPages = Math.ceil(totalCount / mergedOptions.pageSize!);
  const hasMore = currentPage < totalPages;

  // Get token IDs for current page
  const currentPageTokenIds = useMemo(() => {
    const startIndex = (currentPage - 1) * mergedOptions.pageSize!;
    const endIndex = startIndex + mergedOptions.pageSize!;
    return sortedTokenIds.slice(startIndex, endIndex);
  }, [sortedTokenIds, currentPage, mergedOptions.pageSize]);

  // Step 6: Load metadata with caching
  const metadataQuery = useQuery({
    queryKey: ["chickens-metadata-cached", currentPageTokenIds],
    queryFn: async () => {
      if (currentPageTokenIds.length === 0) return {};

      // Get cached metadata first
      const cachedMetadata =
        ChickenCache.getCachedMetadata(currentPageTokenIds);
      const uncachedTokenIds = ChickenCache.getUncachedTokenIds(
        currentPageTokenIds,
        "metadata"
      );

      const newMetadata: Record<number, any> = {};

      // Only fetch metadata for uncached chickens
      if (uncachedTokenIds.length > 0) {
        const CHICKEN_API = process.env.NEXT_PUBLIC_CHICKEN_API_URL || "";
        const response = await fetch(`${CHICKEN_API}/batch`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ ids: uncachedTokenIds }),
        });

        if (!response.ok) throw new Error(`API error: ${response.status}`);
        const batchResponse = await response.json();

        batchResponse.results?.forEach((item: any) => {
          if (item.status === 200 && item.data) {
            newMetadata[item.id] = item.data;
          }
        });

        // Cache the new metadata
        const metadataToCache: Record<number, any> = {};
        Object.entries(newMetadata).forEach(([tokenId, metadata]) => {
          const levelAttribute = metadata.attributes?.find(
            (attr: IAttribute) => attr.trait_type === "Level"
          );

          metadataToCache[Number(tokenId)] = {
            tokenId: Number(tokenId),
            metadata,
            image:
              metadata.image ||
              `https://chicken-api-ivory.vercel.app/api/image/${tokenId}.png`,
            type: getChickenTypeFromMetadata(metadata),
            level: Number(levelAttribute?.value) || 1,
            timestamp: Date.now(),
          };
        });

        ChickenCache.setCachedMetadata(metadataToCache);
      }

      // Combine cached and new metadata
      const combinedMetadata: Record<number, any> = {};

      // Add cached metadata
      Object.values(cachedMetadata).forEach((cached) => {
        combinedMetadata[cached.tokenId] = cached.metadata;
      });

      // Add new metadata
      Object.assign(combinedMetadata, newMetadata);

      return combinedMetadata;
    },
    enabled: currentPageTokenIds.length > 0,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  // Step 7: Load rental data with caching
  const rentalQuery = useQuery({
    queryKey: ["chickens-rental-cached", currentPageTokenIds],
    queryFn: async () => {
      if (currentPageTokenIds.length === 0 || !address) return {};

      try {
        // Get cached rental data first
        const cachedRentalData =
          ChickenCache.getCachedRentalData(currentPageTokenIds);
        const uncachedTokenIds = ChickenCache.getUncachedTokenIds(
          currentPageTokenIds,
          "rental"
        );

        let newRentalData: Record<number, any> = {};

        // Only fetch rental data for uncached chickens
        if (uncachedTokenIds.length > 0) {
          const response =
            await DelegationAPI.getChickenRentalsBulk(uncachedTokenIds);
          newRentalData = response.data || {};

          // Cache the new rental data
          const rentalToCache: Record<number, any> = {};
          Object.entries(newRentalData).forEach(([tokenId, rentalData]) => {
            const rentalStatus = getChickenRentalStatusFromData(rentalData);

            rentalToCache[Number(tokenId)] = {
              tokenId: Number(tokenId),
              rentalData,
              isAvailable: rentalStatus.isAvailable,
              rentalStatus,
              timestamp: Date.now(),
            };
          });

          ChickenCache.setCachedRentalData(rentalToCache);
        }

        // Combine cached and new rental data
        const combinedRentalData: Record<number, any> = {};

        // Add cached rental data
        Object.values(cachedRentalData).forEach((cached) => {
          combinedRentalData[cached.tokenId] = cached.rentalData;
        });

        // Add new rental data
        Object.assign(combinedRentalData, newRentalData);

        return combinedRentalData;
      } catch (error) {
        console.error("Failed to fetch rental data:", error);
        return {};
      }
    },
    enabled: currentPageTokenIds.length > 0 && !!address,
    staleTime: 30 * 1000, // 30 seconds for rental data
    refetchOnWindowFocus: false,
  });

  // Step 8: Load battle stats for current page chickens
  const battleStatsQuery = useBulkBattleStats(currentPageTokenIds);

  // Step 9: Transform data to delegation format (using cached + fresh data)
  const chickensForDelegation: IChickenForDelegationProgressive[] =
    useMemo(() => {
      const metadata = metadataQuery.data || {};
      const rentalData = rentalQuery.data || {};
      const battleStats = battleStatsQuery.data || {};

      // Also get cached data for immediate display
      const cachedMetadata =
        ChickenCache.getCachedMetadata(currentPageTokenIds);
      const cachedRentalData =
        ChickenCache.getCachedRentalData(currentPageTokenIds);

      return (
        currentPageTokenIds
          .map((tokenId) => {
            // Use fresh data if available, otherwise use cached data
            const chickenMetadata =
              metadata[tokenId] || cachedMetadata[tokenId]?.metadata;
            const rental =
              rentalData[tokenId] || cachedRentalData[tokenId]?.rentalData;
            const chickenBattleStats = battleStats[tokenId];

            // Determine type from token ID ranges or metadata or cache
            let type = "ordinary";
            if (tokenIdsByType.genesis.includes(tokenId)) {
              type = "genesis";
            } else if (tokenIdsByType.legacy.includes(tokenId)) {
              type = "legacy";
            } else if (chickenMetadata) {
              type = getChickenTypeFromMetadata(chickenMetadata);
            } else if (cachedMetadata[tokenId]) {
              type = cachedMetadata[tokenId].type;
            }

            // Get level from battle stats first, then metadata or cache
            let level = 1;
            if (chickenBattleStats?.level) {
              level = chickenBattleStats.level;
            } else if (chickenMetadata) {
              const levelAttribute = chickenMetadata.attributes?.find(
                (attr: IAttribute) => attr.trait_type === "Level"
              );
              level = Number(levelAttribute?.value) || 1;
            } else if (cachedMetadata[tokenId]) {
              level = cachedMetadata[tokenId].level;
            }

            // Calculate win rate from battle stats
            let winRate = 0;
            if (chickenBattleStats) {
              const totalBattles =
                (chickenBattleStats.wins || 0) +
                (chickenBattleStats.losses || 0);
              winRate =
                totalBattles > 0
                  ? Math.round(
                      ((chickenBattleStats.wins || 0) / totalBattles) * 100
                    )
                  : 0;
            }

            // Get daily feathers from metadata
            const dailyFeathers = chickenMetadata
              ? getDailyFeathers(chickenMetadata)
              : undefined;

            // Get breed count from metadata
            let breedCount = undefined;
            if (chickenMetadata) {
              const breedCountAttribute = chickenMetadata.attributes?.find(
                (attr: IAttribute) => attr.trait_type === "Breed Count"
              );
              breedCount = Number(breedCountAttribute?.value) || 0;
            }

            // Get rental status from data or cache
            let rentalStatus;
            let isAvailable = true;
            if (rental !== undefined) {
              rentalStatus = getChickenRentalStatusFromData(rental);
              isAvailable = rentalStatus.isAvailable;
            } else if (cachedRentalData[tokenId]) {
              rentalStatus = cachedRentalData[tokenId].rentalStatus;
              isAvailable = cachedRentalData[tokenId].isAvailable;
            } else {
              rentalStatus = getChickenRentalStatusFromData(null);
            }

            // Get image from metadata or cache
            const image =
              chickenMetadata?.image ||
              cachedMetadata[tokenId]?.image ||
              `https://chicken-api-ivory.vercel.app/api/image/${tokenId}.png`;

            return {
              tokenId,
              image,
              type,
              level,
              isAvailable,
              rentalStatus,
              metadata: chickenMetadata,
              dailyFeathers,
              breedCount,
              cooldownEndsAt: undefined, // This would need additional API call
              winRate,
              battleStats: chickenBattleStats,
            };
          })
          // Filter out chickens that are listed, delegated, or rented
          .filter((chicken) => chicken.isAvailable)
      );
    }, [
      currentPageTokenIds,
      metadataQuery.data,
      rentalQuery.data,
      battleStatsQuery.data,
      tokenIdsByType,
    ]);

  // Pagination functions
  const loadMore = useCallback(() => {
    if (hasMore) {
      setCurrentPage((prev) => prev + 1);
    }
  }, [hasMore]);

  const goToPage = useCallback(
    (page: number) => {
      if (page >= 1 && page <= totalPages) {
        setCurrentPage(page);
      }
    },
    [totalPages]
  );

  const goToPreviousPage = useCallback(() => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  }, [currentPage]);

  const goToNextPage = useCallback(() => {
    if (hasMore) {
      setCurrentPage((prev) => prev + 1);
    }
  }, [hasMore]);

  // Set visible chickens for progressive loading (placeholder for compatibility)
  const setVisibleChickens = useCallback((tokenIds: number[]) => {
    // In the optimized version, we don't need to track visible chickens
    // since we only load metadata for the current page
    console.log("Visible chickens:", tokenIds.length);
  }, []);

  // Cache management functions
  const clearCache = useCallback(() => {
    ChickenCache.clearAllCaches();
    // Refetch current data after clearing cache
    metadataQuery.refetch();
    rentalQuery.refetch();
    battleStatsQuery.refetch();
  }, [metadataQuery, rentalQuery, battleStatsQuery]);

  const getCacheStats = useCallback((): ICacheStats => {
    return ChickenCache.getCacheStats();
  }, []);

  // Stats for UI display
  const stats = useMemo(() => {
    const genesisCount = tokenIdsByType.genesis.length;
    const legacyCount = tokenIdsByType.legacy.length;
    const totalCount = genesisCount + legacyCount;

    return {
      total: totalCount,
      genesis: genesisCount,
      legacy: legacyCount,
      ordinary: 0, // No ordinary chickens in contracts
    };
  }, [tokenIdsByType]);

  return {
    // Chicken data
    chickens: chickensForDelegation,
    allChickens: chickensForDelegation, // For compatibility
    currentPageChickens: chickensForDelegation,

    // Pagination
    currentPage,
    totalPages,
    hasMore,
    loadMore,
    goToPage,
    goToPreviousPage,
    goToNextPage,
    totalCount,

    // Progressive loading controls
    setVisibleChickens,

    // Loading states
    isLoading:
      tokenIdsLoading ||
      metadataQuery.isLoading ||
      rentalQuery.isLoading ||
      battleStatsQuery.isLoading,
    isLoadingBasic: tokenIdsLoading,
    error:
      tokenIdsError ||
      metadataQuery.error ||
      rentalQuery.error ||
      battleStatsQuery.error,

    // Connection state
    isConnected,
    address,

    // Stats
    stats,

    // Filter/sort options
    filterType: mergedOptions.filterType,
    sortBy: mergedOptions.sortBy,
    sortOrder: mergedOptions.sortOrder,

    // Cache management
    clearCache,
    getCacheStats,
  };
}

export function useProgressiveChickensForDelegation(
  options: Partial<IProgressiveDelegationOptions> = {}
) {
  const { address, isConnected } = useStateContext();
  const mergedOptions = { ...DEFAULT_DELEGATION_OPTIONS, ...options };

  // Use the progressive chicken info fetcher
  const progressiveResponse = useConnectedWalletProgressiveChickenInfo({
    pageSize: mergedOptions.pageSize,
    enableAutoLoad: mergedOptions.enableAutoLoad,
    preloadNextPage: mergedOptions.preloadNextPage,
  });

  // Transform basic chicken data to delegation format
  const chickensForDelegation: IChickenForDelegationProgressive[] =
    useMemo(() => {
      const basicChickens = progressiveResponse.basicChickens;

      return progressiveResponse.loadedTokenIds.map((tokenId) => {
        const basicChicken = basicChickens[tokenId];

        if (!basicChicken) {
          // Return minimal data if basic info not loaded yet
          return {
            tokenId,
            image: `https://chicken-api-ivory.vercel.app/api/image/${tokenId}.png`,
            type: "ordinary",
            level: 1,
            isAvailable: false,
            rentalStatus: {
              isAvailable: false,
              rentalStatus: "available",
              statusLabel: "Loading...",
            },
          };
        }

        return {
          tokenId: basicChicken.tokenId,
          image: basicChicken.image,
          type: basicChicken.type?.toLowerCase(),
          level: basicChicken.level,
          isAvailable: basicChicken.isAvailable,
          rentalStatus: basicChicken.rentalStatus,

          // These will be populated by interactive/detailed loading phases
          metadata: undefined,
          dailyFeathers: undefined,
          breedCount: undefined,
          cooldownEndsAt: undefined,
          winRate: undefined,
          battleStats: undefined,
        };
      });
    }, [progressiveResponse.basicChickens, progressiveResponse.loadedTokenIds]);

  // Apply filtering
  const filteredChickens = useMemo(() => {
    // First filter out unavailable chickens (listed, delegated, or rented)
    const availableChickens = chickensForDelegation.filter(
      (chicken) => chicken.isAvailable
    );

    // Then apply type filtering
    if (mergedOptions.filterType === "all") {
      return availableChickens;
    }

    return availableChickens.filter(
      (chicken) =>
        chicken.type?.toLowerCase() === mergedOptions.filterType?.toLowerCase()
    );
  }, [chickensForDelegation, mergedOptions.filterType]);

  // Apply sorting
  const sortedChickens = useMemo(() => {
    const sorted = [...filteredChickens];

    sorted.sort((a, b) => {
      let comparison = 0;

      switch (mergedOptions.sortBy) {
        case "tokenId":
          comparison = a.tokenId - b.tokenId;
          break;
        case "level":
          comparison = (a.level || 0) - (b.level || 0);
          break;
        case "type":
          comparison = (a.type || "").localeCompare(b.type || "");
          break;
        default:
          comparison = a.tokenId - b.tokenId;
      }

      return mergedOptions.sortOrder === "desc" ? -comparison : comparison;
    });

    return sorted;
  }, [filteredChickens, mergedOptions.sortBy, mergedOptions.sortOrder]);

  // Group chickens by type for easy access
  const chickensByType = useMemo(() => {
    const ordinary = sortedChickens.filter(
      (chicken) => chicken.type?.toLowerCase() === "ordinary"
    );
    const legacy = sortedChickens.filter(
      (chicken) => chicken.type?.toLowerCase() === "legacy"
    );
    const genesis = sortedChickens.filter(
      (chicken) => chicken.type?.toLowerCase() === "genesis"
    );

    return { ordinary, legacy, genesis };
  }, [sortedChickens]);

  // Calculate pagination info for filtered results
  const currentPageChickens = useMemo(() => {
    const startIndex =
      (progressiveResponse.currentPage - 1) * mergedOptions.pageSize!;
    const endIndex = startIndex + mergedOptions.pageSize!;
    return sortedChickens.slice(startIndex, endIndex);
  }, [sortedChickens, progressiveResponse.currentPage, mergedOptions.pageSize]);

  // Stats for UI display
  const stats = useMemo(() => {
    const available = sortedChickens.filter(
      (chicken) => chicken.isAvailable
    ).length;
    const listed = sortedChickens.filter(
      (chicken) => chicken.rentalStatus?.rentalStatus === "listed"
    ).length;
    const delegated = sortedChickens.filter(
      (chicken) => chicken.rentalStatus?.rentalStatus === "delegated"
    ).length;
    const rented = sortedChickens.filter(
      (chicken) => chicken.rentalStatus?.rentalStatus === "rented"
    ).length;

    return {
      total: sortedChickens.length,
      available,
      listed,
      delegated,
      rented,
      unavailable: sortedChickens.length - available,
    };
  }, [sortedChickens]);

  return {
    // Chicken data
    chickens: sortedChickens,
    currentPageChickens,
    chickensByType,

    // Pagination
    currentPage: progressiveResponse.currentPage,
    totalPages: progressiveResponse.totalPages,
    hasMore: progressiveResponse.hasMore,
    loadMore: progressiveResponse.loadMore,
    totalCount: progressiveResponse.totalCount,

    // Loading states
    isLoading: progressiveResponse.isLoading,
    isLoadingBasic: progressiveResponse.isLoadingBasic,
    error: progressiveResponse.error,

    // Connection state
    isConnected,
    address,

    // Stats
    stats,

    // Filter/sort options
    filterType: mergedOptions.filterType,
    sortBy: mergedOptions.sortBy,
    sortOrder: mergedOptions.sortOrder,
  };
}

// Helper hook for specific chicken types
export function useProgressiveChickensByType(
  type: "ordinary" | "legacy" | "genesis",
  options: Partial<IProgressiveDelegationOptions> = {}
) {
  return useProgressiveChickensForDelegation({
    ...options,
    filterType: type,
  });
}

// Helper hook with search functionality
export function useProgressiveChickensWithSearch(
  searchQuery: string,
  options: Partial<IProgressiveDelegationOptions> = {}
) {
  const baseResult = useProgressiveChickensForDelegation(options);

  const filteredChickens = useMemo(() => {
    if (!searchQuery.trim()) {
      return baseResult.chickens;
    }

    const query = searchQuery.toLowerCase();
    return baseResult.chickens.filter((chicken) => {
      return (
        chicken.tokenId.toString().includes(query) ||
        chicken.metadata?.name?.toLowerCase().includes(query) ||
        chicken.metadata?.nickname?.toLowerCase().includes(query) ||
        chicken.type?.toLowerCase().includes(query)
      );
    });
  }, [baseResult.chickens, searchQuery]);

  return {
    ...baseResult,
    chickens: filteredChickens,
    totalCount: filteredChickens.length,
  };
}
