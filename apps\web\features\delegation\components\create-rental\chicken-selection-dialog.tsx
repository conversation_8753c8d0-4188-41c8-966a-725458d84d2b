"use client";

import {
  Search,
  Star,
  WifiOff,
  ChevronLeft,
  ChevronRight,
  Loader2,
} from "lucide-react";
import Image from "next/image";
import { useState, useMemo } from "react";
import { <PERSON><PERSON>, Modal, TextField, cn } from "ui";
import {
  useOptimizedChickensForDelegation,
  IChickenForDelegationProgressive,
} from "../../../chickens/hooks/delegation/useProgressiveChickensForDelegation";
import { IChickenSelection } from "../../types/delegation.types";

interface IChickenSelectionDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSelect?: (chicken: IChickenForDelegationProgressive) => void;
  selectedChickenId?: number | null;
  // Bulk selection props
  isBulkMode?: boolean;
  selectedChickens?: IChickenSelection;
  onConfirmBulkSelection?: (
    selectedChickens: IChickenSelection,
    chickensData: IChickenForDelegationProgressive[]
  ) => void;
  maxSelections?: number;
}

export function ChickenSelectionDialog({
  isOpen,
  onOpenChange,
  onSelect,
  selectedChickenId,
  isBulkMode = false,
  selectedChickens = {},
  onConfirmBulkSelection,
  maxSelections = 20,
}: IChickenSelectionDialogProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [localSelectedChickens, setLocalSelectedChickens] =
    useState<IChickenSelection>(selectedChickens);
  const [selectedChickensData, setSelectedChickensData] = useState<
    Record<number, IChickenForDelegationProgressive>
  >({});

  // Use optimized loading with search and filtering
  const {
    chickens: currentPageChickens,
    isLoading,
    error,
    isConnected,
    currentPage,
    totalPages,
    hasMore,
    goToPreviousPage,
    goToNextPage,
    totalCount,
    stats,
  } = useOptimizedChickensForDelegation(searchQuery, {
    pageSize: 20, // 20 chickens per page
    filterType: filterType as "all" | "legacy" | "genesis" | "ordinary",
    enableAutoLoad: false, // Disable auto-load for pagination
  });

  // Use current page chickens for display
  const chickensToDisplay = useMemo(
    () => currentPageChickens || [],
    [currentPageChickens]
  );

  const getTypeColor = (type: string) => {
    switch (type?.toLowerCase()) {
      case "genesis":
        return "bg-orange-500 text-white shadow-lg shadow-orange-500/30";
      case "legacy":
        return "bg-blue-500 text-white shadow-lg shadow-blue-500/30";
      case "ordinary":
        return "bg-gray-600 text-white shadow-lg shadow-gray-600/30";
      default:
        return "bg-gray-600 text-white shadow-lg shadow-gray-600/30";
    }
  };

  // Calculate selection counts
  const selectedCount = Object.values(localSelectedChickens).filter(
    Boolean
  ).length;
  const availableChickens = chickensToDisplay.filter(
    (chicken) => chicken.isAvailable
  );
  const canSelectMore = selectedCount < maxSelections;

  // Handle single selection
  const handleSelect = (chicken: IChickenForDelegationProgressive) => {
    if (!chicken.isAvailable) return;

    if (isBulkMode) {
      handleToggleChicken(chicken.tokenId);
    } else {
      onSelect?.(chicken);
      onOpenChange(false);
    }
  };

  // Handle bulk selection toggle
  const handleToggleChicken = (chickenId: number) => {
    if (!canSelectMore && !localSelectedChickens[chickenId]) {
      return; // Can't select more chickens
    }

    const chicken = chickensToDisplay.find((c) => c.tokenId === chickenId);

    setLocalSelectedChickens((prev) => ({
      ...prev,
      [chickenId]: !prev[chickenId],
    }));

    // Store or remove chicken data
    setSelectedChickensData((prev) => {
      const newData = { ...prev };
      if (!localSelectedChickens[chickenId] && chicken) {
        // Selecting chicken - store its data
        newData[chickenId] = chicken;
      } else {
        // Deselecting chicken - remove its data
        delete newData[chickenId];
      }
      return newData;
    });
  };

  // Handle select all available chickens on current page
  const handleSelectAllPage = () => {
    const newSelection = { ...localSelectedChickens };
    const newChickensData = { ...selectedChickensData };
    let currentSelectionCount = selectedCount;

    availableChickens.forEach((chicken) => {
      // Only select if not already selected and under the limit
      if (
        !newSelection[chicken.tokenId] &&
        currentSelectionCount < maxSelections
      ) {
        newSelection[chicken.tokenId] = true;
        newChickensData[chicken.tokenId] = chicken;
        currentSelectionCount++;
      }
    });

    setLocalSelectedChickens(newSelection);
    setSelectedChickensData(newChickensData);
  };

  // Handle deselect all chickens
  const handleDeselectAll = () => {
    setLocalSelectedChickens({});
    setSelectedChickensData({});
  };

  // Handle confirm bulk selection
  const handleConfirmSelection = () => {
    // Get the selected chicken data from our cache
    const selectedChickenDataArray = Object.values(selectedChickensData);
    onConfirmBulkSelection?.(localSelectedChickens, selectedChickenDataArray);
    onOpenChange(false);
  };

  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
      <Modal.Content
        size="5xl"
        classNames={{
          content: "max-h-[90vh] h-auto flex flex-col",
          overlay: "overflow-hidden",
        }}
      >
        <Modal.Header>
          <Modal.Title>
            {isBulkMode
              ? "Select Chickens for Bulk Listing"
              : "Select a Chicken to List"}
          </Modal.Title>
          <Modal.Description>
            {isBulkMode
              ? `Choose multiple chickens to list for rent. ${selectedCount}/${maxSelections} selected`
              : "Choose which chicken you want to rent out to other players"}
          </Modal.Description>
        </Modal.Header>

        <Modal.Body className="flex flex-col gap-6 flex-1 min-h-0">
          {/* Search Section */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 z-10" />
            <TextField
              placeholder="Search chickens by Chicken ID..."
              value={searchQuery}
              onChange={setSearchQuery}
              className="pl-10 w-full"
            />
          </div>

          {/* Filter Badges */}
          <div className="flex justify-center gap-2 flex-wrap">
            <button
              onClick={() => setFilterType("all")}
              className={cn(
                "px-3 py-1 text-xs rounded-full font-medium transition-all duration-200",
                filterType === "all"
                  ? "bg-white text-black"
                  : "bg-white/20 text-gray-400 border border-white/30 hover:bg-white/30"
              )}
            >
              All ({stats?.total || 0})
            </button>
            <button
              onClick={() => setFilterType("legacy")}
              className={cn(
                "px-3 py-1 text-xs rounded-full font-medium transition-all duration-200",
                filterType === "legacy"
                  ? "bg-blue-500 text-white"
                  : "bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:bg-blue-500/30"
              )}
            >
              Legacy ({stats?.legacy || 0})
            </button>
            <button
              onClick={() => setFilterType("genesis")}
              className={cn(
                "px-3 py-1 text-xs rounded-full font-medium transition-all duration-200",
                filterType === "genesis"
                  ? "bg-orange-500 text-white"
                  : "bg-orange-500/20 text-orange-400 border border-orange-500/30 hover:bg-orange-500/30"
              )}
            >
              Genesis ({stats?.genesis || 0})
            </button>
          </div>

          {/* Bulk Selection Controls */}
          {isBulkMode && (
            <div className="flex items-center justify-between p-3 bg-stone-800/50 rounded-lg border border-stone-600/30">
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-300">
                  {selectedCount} of {maxSelections} chickens selected
                </span>
                {selectedCount > 0 && (
                  <span className="text-xs text-blue-400">
                    {selectedCount === 1
                      ? "1 chicken"
                      : `${selectedCount} chickens`}{" "}
                    ready to list
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  size="small"
                  appearance="outline"
                  onPress={handleSelectAllPage}
                  isDisabled={!canSelectMore || availableChickens.length === 0}
                  className="text-xs"
                >
                  Select Page
                </Button>
                <Button
                  size="small"
                  appearance="outline"
                  onPress={handleDeselectAll}
                  isDisabled={selectedCount === 0}
                  className="text-xs"
                >
                  Clear All
                </Button>
              </div>
            </div>
          )}

          {/* Chickens Display */}
          <div className="flex-1 flex flex-col">
            {!isConnected ? (
              <div className="text-center py-12 text-gray-400">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-stone-800 flex items-center justify-center">
                  <WifiOff className="w-6 h-6" />
                </div>
                <p className="text-lg font-medium mb-2">Wallet not connected</p>
                <p className="text-sm">
                  Please connect your wallet to view your chickens
                </p>
              </div>
            ) : isLoading ? (
              <div className="text-center py-16">
                {/* Animated Loading Spinner */}
                <div className="relative w-20 h-20 mx-auto mb-6">
                  {/* Outer ring */}
                  <div className="absolute inset-0 rounded-full border-4 border-stone-700"></div>
                  {/* Spinning ring */}
                  <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-orange-500 animate-spin"></div>
                  {/* Inner pulsing dot */}
                  <div className="absolute inset-4 rounded-full bg-orange-500/20 animate-pulse"></div>
                  {/* Center dot */}
                  <div className="absolute inset-6 rounded-full bg-orange-500"></div>
                </div>

                {/* Loading Text */}
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-white">
                    Loading chickens...
                  </h3>
                  <p className="text-gray-400 text-sm">
                    Fetching your chickens from the blockchain
                  </p>
                </div>

                {/* Loading Progress Dots */}
                <div className="flex justify-center space-x-1 mt-6">
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce"></div>
                </div>
              </div>
            ) : error ? (
              <div className="text-center py-12 text-gray-400">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-800 flex items-center justify-center">
                  <WifiOff className="w-6 h-6" />
                </div>
                <p className="text-lg font-medium mb-2">
                  Error loading chickens
                </p>
                <p className="text-sm">
                  Failed to fetch your chickens. Please try again.
                </p>
              </div>
            ) : (
              chickensToDisplay.length === 0 && (
                <div className="text-center py-12 text-gray-400">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-stone-800 flex items-center justify-center">
                    <Search className="w-6 h-6" />
                  </div>
                  <p className="text-lg font-medium mb-2">No chickens found</p>
                  <p className="text-sm">
                    Try adjusting your search or filter criteria
                  </p>
                </div>
              )
            )}

            {chickensToDisplay.length > 0 && !isLoading && (
              <div className="flex flex-col flex-1 gap-6">
                {/* Simple Grid Container */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
                  {chickensToDisplay.map((chicken) => (
                    <ChickenGridCard
                      key={chicken.tokenId}
                      chicken={chicken}
                      isSelected={selectedChickenId === chicken.tokenId}
                      onSelect={handleSelect}
                      getTypeColor={getTypeColor}
                      isBulkMode={isBulkMode}
                      isChecked={
                        localSelectedChickens[chicken.tokenId] || false
                      }
                    />
                  ))}
                </div>

                {/* Pagination Controls */}
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-400">
                    Showing {chickensToDisplay.length * currentPage} of{" "}
                    {totalCount} chickens
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      appearance="outline"
                      onPress={goToPreviousPage}
                      isDisabled={currentPage <= 1 || isLoading}
                      className="flex items-center gap-2"
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Previous
                    </Button>

                    <span className="text-sm text-gray-400 px-4">
                      Page {currentPage} of {totalPages}
                    </span>

                    <Button
                      appearance="outline"
                      onPress={goToNextPage}
                      isDisabled={!hasMore || isLoading}
                      className="flex items-center gap-2"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        <>
                          Next
                          <ChevronRight className="w-4 h-4" />
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Modal.Body>

        <Modal.Footer>
          <div className="flex items-center justify-between w-full">
            <Button appearance="outline" onPress={() => onOpenChange(false)}>
              Cancel
            </Button>

            {isBulkMode && (
              <div className="flex items-center gap-3">
                <span className="text-sm text-gray-400">
                  {selectedCount} chickens selected
                </span>
                <Button
                  onPress={handleConfirmSelection}
                  isDisabled={selectedCount === 0}
                  className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold"
                >
                  Confirm Selection
                </Button>
              </div>
            )}
          </div>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
}

// Grid Card Component
function ChickenGridCard({
  chicken,
  isSelected,
  onSelect,
  getTypeColor,
  isBulkMode = false,
  isChecked = false,
}: {
  chicken: IChickenForDelegationProgressive;
  isSelected: boolean;
  onSelect: (chicken: IChickenForDelegationProgressive) => void;
  getTypeColor: (type: string) => string;
  isBulkMode?: boolean;
  isChecked?: boolean;
}) {
  const isDisabled = !chicken.isAvailable;

  return (
    <div
      onClick={() => onSelect(chicken)}
      className={cn(
        "group relative rounded-xl transition-all duration-300 overflow-hidden bg-stone-800 border border-stone-700",
        isDisabled
          ? "opacity-60 cursor-not-allowed"
          : "cursor-pointer hover:border-stone-500 hover:shadow-lg hover:shadow-stone-500/10",
        isBulkMode && isChecked
          ? "border-blue-500 border-2 shadow-lg shadow-blue-500/20"
          : isSelected
            ? "border-yellow-500 shadow-lg shadow-yellow-500/20"
            : ""
      )}
    >
      {/* Chicken Image with Rarity Badge */}
      <div className="aspect-square relative overflow-hidden bg-gradient-to-br from-stone-700 to-stone-800">
        <Image
          src={chicken.image}
          alt={`Chicken #${chicken.tokenId}`}
          width={200}
          height={200}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        />

        {/* Rarity Badge */}
        {chicken.type && (
          <div className="absolute top-3 left-3">
            <span
              className={cn(
                "px-2 py-1 text-xs rounded-md font-medium",
                getTypeColor(chicken.type)
              )}
            >
              {chicken.type}
            </span>
          </div>
        )}

        {/* Unavailable Overlay */}
        {!chicken.isAvailable && (
          <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
            <span
              className={`text-xs font-medium px-2 py-1 rounded-full border ${
                chicken.rentalStatus?.rentalStatus === "rented"
                  ? "text-red-400 bg-red-500/20 border-red-500/30"
                  : chicken.rentalStatus?.rentalStatus === "delegated"
                    ? "text-purple-400 bg-purple-500/20 border-purple-500/30"
                    : chicken.rentalStatus?.rentalStatus === "listed"
                      ? "text-yellow-400 bg-yellow-500/20 border-yellow-500/30"
                      : chicken.rentalStatus?.statusLabel === "Fainted"
                        ? "text-red-400 bg-red-500/20 border-red-500/30"
                        : chicken.rentalStatus?.statusLabel === "Dead"
                          ? "text-gray-400 bg-gray-500/20 border-gray-500/30"
                          : chicken.rentalStatus?.statusLabel === "Breeding"
                            ? "text-blue-400 bg-blue-500/20 border-blue-500/30"
                            : "text-gray-400 bg-gray-500/20 border-gray-500/30"
              }`}
            >
              {chicken.rentalStatus?.statusLabel || "Unavailable"}
            </span>
          </div>
        )}
      </div>

      {/* Chicken Info */}
      <div className="p-4 space-y-3">
        {/* Chicken Name */}
        <div>
          <h3 className="font-semibold text-white">
            {chicken.metadata?.name || `Chicken #${chicken.tokenId}`}
          </h3>
        </div>

        {/* Stats Row */}
        <div className="flex items-center justify-between">
          {/* Level */}
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 text-yellow-400" />
            <span className="text-white text-sm font-medium">
              Level {chicken.level || 1}
            </span>
          </div>

          {/* Daily Feathers */}
          <div className="flex items-center gap-1">
            <span className="text-orange-400 text-sm">🪶</span>
            <span className="text-white text-sm font-medium">
              {chicken.dailyFeathers || 0}/day
            </span>
          </div>
        </div>

        {/* Win Rate */}
        <div className="text-center">
          <span className="text-gray-400 text-sm">Win Rate: </span>
          <span className="text-white font-medium">
            {chicken.winRate !== undefined ? chicken.winRate.toFixed(1) : "0.0"}
            %
          </span>
        </div>
      </div>
    </div>
  );
}
