import { ChevronDown, Search, CheckSquare } from "lucide-react";

type SortableAttribute =
  | "id"
  | "hp"
  | "level"
  | "attack"
  | "defense"
  | "speed"
  | "ferocity"
  | "cockrage"
  | "evasion"
  | "mmr";

type FilterType =
  | "all"
  | "genesis"
  | "legacy"
  | "rented"
  | "delegated"
  | "listed";

interface HealInfo {
  healsRemaining: number;
  maxHeals: number;
  resetTime: string;
}

interface EnhancedInventoryControlsProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  sortAttribute: SortableAttribute;
  sortOrder: "asc" | "desc";
  onSortChange: (attribute: SortableAttribute, order: "asc" | "desc") => void;
  showSortDropdown: boolean;
  onToggleSortDropdown: () => void;
  filterType: FilterType;
  onFilterChange: (filter: FilterType) => void;
  totalCount: number;
  activeTab?: "owned" | "delegated-out";
  // Heal info props
  healInfo?: HealInfo | null;
  healInfoLoading?: boolean;
  onRefreshHealInfo?: () => void;
  // Bulk action props
  isBulkMode?: boolean;
  bulkActionSummary?: {
    totalSelected: number;
    canCancelDelegation: number;
    canUnlistFromMarket: number;
  };
  onToggleBulkMode?: () => void;
}

export function EnhancedInventoryControls({
  searchQuery,
  onSearchChange,
  sortAttribute,
  sortOrder,
  onSortChange,
  showSortDropdown,
  onToggleSortDropdown,
  filterType,
  onFilterChange,
  totalCount,
  activeTab = "owned",
  healInfo,
  healInfoLoading,
  onRefreshHealInfo,
  isBulkMode = false,
  bulkActionSummary,
  onToggleBulkMode,
}: EnhancedInventoryControlsProps) {
  const getSortDisplayName = (attribute: SortableAttribute) => {
    switch (attribute) {
      case "id":
        return "ID";
      case "hp":
        return "HP";
      case "mmr":
        return "MMR";
      default:
        return attribute.charAt(0).toUpperCase() + attribute.slice(1);
    }
  };

  const getFilterDisplayName = (filter: FilterType) => {
    switch (filter) {
      case "all":
        return "All";
      case "genesis":
        return "Genesis";
      case "legacy":
        return "Legacy";
      case "rented":
        return activeTab === "delegated-out" ? "Rented out" : "Rented to Me";
      case "delegated":
        return activeTab === "delegated-out"
          ? "Delegated out"
          : "Delegated to Me";
      case "listed":
        return "Listed for Rent";
      default:
        return filter;
    }
  };

  // Get available filters based on active tab
  const getAvailableFilters = (): FilterType[] => {
    if (activeTab === "delegated-out") {
      return ["all", "listed", "rented", "delegated"];
    } else {
      return ["all", "genesis", "legacy", "rented", "delegated"];
    }
  };

  return (
    <div className="flex flex-col lg:flex-row lg:justify-between gap-4 my-6">
      {/* Mobile: Stack everything vertically */}
      <div className="space-y-4">
        {/* Search and Sort Row */}
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Search */}
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
              <Search className="h-5 w-5" />
            </div>
            <input
              type="text"
              className="bg-stone-700 text-white pl-10 pr-4 py-2 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-yellow-500"
              placeholder="Search by ID..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </div>

          {/* Sort Control - Only show for owned tab */}
          {activeTab !== "delegated-out" && (
            <div className="relative sm:w-auto">
              <button
                className="flex items-center justify-center gap-2 bg-stone-700 text-white px-4 py-2 rounded-lg hover:bg-stone-600 transition-colors w-full sm:w-auto"
                onClick={onToggleSortDropdown}
              >
                <span className="hidden sm:inline">Sort by</span>
                <span className="sm:hidden">Sort</span>
                <span>{getSortDisplayName(sortAttribute)}</span>
                <span>{sortOrder === "desc" ? "↓" : "↑"}</span>
                <ChevronDown className="h-4 w-4" />
              </button>

              {showSortDropdown && (
                <div className="absolute right-0 mt-1 w-40 bg-stone-800 border border-stone-600 rounded-lg shadow-lg overflow-hidden z-40">
                  {(["id"] as SortableAttribute[]).map((attr) => (
                    <button
                      key={attr}
                      className={`w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors ${
                        sortAttribute === attr ? "bg-stone-700" : ""
                      }`}
                      onClick={() => {
                        onSortChange(attr, sortOrder);
                        onToggleSortDropdown();
                      }}
                    >
                      {getSortDisplayName(attr)}
                    </button>
                  ))}
                  <div className="border-t border-stone-600 my-1"></div>
                  <button
                    className="w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors"
                    onClick={() => {
                      onSortChange(
                        sortAttribute,
                        sortOrder === "asc" ? "desc" : "asc"
                      );
                      onToggleSortDropdown();
                    }}
                  >
                    {sortOrder === "asc" ? "Descending ↓" : "Ascending ↑"}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Filter Controls and Actions */}
        <div className="flex flex-wrap sm:items-center sm:justify-between gap-3">
          <div className="flex flex-wrap items-center gap-2">
            {getAvailableFilters().map((filter) => (
              <button
                key={filter}
                onClick={() => onFilterChange(filter)}
                className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                  filterType === filter
                    ? "bg-yellow-500 text-black"
                    : "bg-stone-700 text-white hover:bg-stone-600"
                }`}
              >
                {getFilterDisplayName(filter)}
              </button>
            ))}

            {/* Bulk Actions Toggle */}
            {onToggleBulkMode && (
              <button
                onClick={onToggleBulkMode}
                className={`flex items-center gap-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                  isBulkMode
                    ? "bg-blue-600 hover:bg-blue-700 text-white"
                    : "bg-stone-700 hover:bg-stone-600 text-white"
                }`}
              >
                <CheckSquare className="h-4 w-4" />
                {isBulkMode ? (
                  <span>
                    Bulk Actions
                    {bulkActionSummary &&
                      bulkActionSummary.totalSelected > 0 && (
                        <span className="ml-1 bg-blue-800 px-2 py-0.5 rounded text-xs">
                          {bulkActionSummary.totalSelected}
                        </span>
                      )}
                  </span>
                ) : (
                  "Bulk Actions"
                )}
              </button>
            )}
          </div>

          {/* Total Count */}
          <div className="text-stone-400 text-sm whitespace-nowrap">
            {totalCount} chicken{totalCount !== 1 ? "s" : ""}
          </div>
        </div>
      </div>

      {/* Heal Info Display - Mobile friendly */}
      {activeTab === "owned" && (
        <div className="flex sm:justify-end">
          <div className="flex items-center gap-2 w-full sm:w-auto">
            <div
              className={`px-4 py-2 rounded-lg flex items-center gap-2 flex-1 sm:flex-initial ${
                healInfoLoading
                  ? "bg-stone-700"
                  : "bg-stone-800 border border-stone-700"
              }`}
            >
              {healInfoLoading ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 rounded-full border-2 border-t-transparent border-blue-500 animate-spin"></div>
                  <span className="text-stone-400">Loading...</span>
                </div>
              ) : healInfo ? (
                <div className="flex flex-col px-2">
                  <span className="text-white font-medium">
                    {healInfo.healsRemaining} / {healInfo.maxHeals} Free Heals
                  </span>
                  <span className="text-xs text-stone-400">
                    Resets at {healInfo.resetTime}
                  </span>
                </div>
              ) : (
                <span className="text-stone-400">No heal info</span>
              )}
            </div>

            {/* Refresh Button */}
            <button
              onClick={onRefreshHealInfo}
              disabled={healInfoLoading}
              className="p-2 bg-stone-700 hover:bg-stone-600 rounded-lg text-white transition-colors flex-shrink-0"
              title="Refresh heal count"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={`${healInfoLoading ? "animate-spin" : ""}`}
              >
                <path d="M21 12a9 9 0 0 1-9 9c-4.97 0-9-4.03-9-9s4.03-9 9-9h3"></path>
                <path d="M21 3v6h-6"></path>
                <path d="M21 9a9 9 0 0 0-9 3"></path>
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
